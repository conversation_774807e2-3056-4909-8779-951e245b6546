"""
ESG Emissions Predictor - Clean & Simple Interface
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import joblib
import warnings
warnings.filterwarnings('ignore')

# Import prescriptive analytics module
try:
    from prescriptive_analytics import ESGPrescriptiveAnalytics
    PRESCRIPTIVE_AVAILABLE = True
except ImportError:
    PRESCRIPTIVE_AVAILABLE = False

# Page configuration
st.set_page_config(
    page_title="ESG Emissions Predictor",
    page_icon="",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Clean, minimal CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f4e79;
        text-align: center;
        margin-bottom: 1rem;
        font-weight: 300;
    }
    .section-header {
        font-size: 1.2rem;
        color: #2c5aa0;
        margin: 1.5rem 0 0.5rem 0;
        font-weight: 500;
    }
    .stMetric {
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 8px;
        border-left: 4px solid #2c5aa0;
    }
    .prediction-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

class ESGStreamlitApp:
    def __init__(self):
        self.load_data()
        self.load_models()
        self.load_prescriptive_analytics()

    def load_data(self):
        """Load the processed datasets"""
        try:
            self.scope1_df = pd.read_csv('enhanced_scope1.csv')
            self.scope2_df = pd.read_csv('enhanced_scope2.csv')
            self.scope3_df = pd.read_csv('enhanced_scope3.csv')

            # Convert date columns
            for df in [self.scope1_df, self.scope2_df, self.scope3_df]:
                df['Date'] = pd.to_datetime(df['Date'])

            self.data_loaded = True
        except FileNotFoundError:
            st.error(" Data files not found. Please run the data generation scripts first.")
            self.data_loaded = False

    def load_models(self):
        """Load trained models if available"""
        self.models_loaded = False
        self.models = {}

        try:
            for scope in ['scope1', 'scope2', 'scope3']:
                model = joblib.load(f'{scope}_best_model.pkl')
                metadata = joblib.load(f'{scope}_model_metadata.pkl')
                self.models[scope] = {
                    'model': model,
                    'metadata': metadata
                }
            self.models_loaded = True
        except FileNotFoundError:
            st.info("Models will be loaded when available.")

    def load_prescriptive_analytics(self):
        """Load prescriptive analytics module"""
        self.prescriptive_loaded = False
        self.prescriptive = None

        if PRESCRIPTIVE_AVAILABLE and self.models_loaded:
            try:
                self.prescriptive = ESGPrescriptiveAnalytics()
                if self.prescriptive.models:
                    self.prescriptive.create_dice_explainers()
                    self.prescriptive_loaded = True
            except Exception as e:
                st.warning(f"Prescriptive analytics not available: {str(e)}")
    
    def show_dashboard(self):
        """Clean, simple dashboard"""
        st.markdown('<h1 class="main-header"> ESG Emissions Predictor</h1>', unsafe_allow_html=True)

        if not self.data_loaded:
            st.error("Please ensure data files are available.")
            return

        # Key metrics in a clean layout
        st.markdown('<div class="section-header">Current Emissions Overview</div>', unsafe_allow_html=True)

        col1, col2, col3 = st.columns(3)

        with col1:
            avg_scope1 = self.scope1_df['scope1_emissions_kg_co2'].mean()
            st.metric("Scope 1 (Direct)", f"{avg_scope1:,.0f} kg CO₂", help="Direct emissions from owned sources")

        with col2:
            avg_scope2 = self.scope2_df['scope2_emissions_kg_co2'].mean()
            st.metric("Scope 2 (Electricity)", f"{avg_scope2:,.0f} kg CO₂", help="Indirect emissions from electricity")

        with col3:
            avg_scope3 = self.scope3_df['scope3_emissions_kg_co2'].mean()
            st.metric("Scope 3 (Supply Chain)", f"{avg_scope3:,.0f} kg CO₂", help="Other indirect emissions")

        # Total emissions highlight
        total_avg = avg_scope1 + avg_scope2 + avg_scope3
        st.markdown(f"""
        <div class="prediction-box">
            <h3>Total Average Emissions</h3>
            <h2>{total_avg:,.0f} kg CO₂</h2>
        </div>
        """, unsafe_allow_html=True)
        
        # Simple trend chart
        st.markdown('<div class="section-header"> Emissions Trends</div>', unsafe_allow_html=True)

        # Prepare monthly data
        scope1_monthly = self.scope1_df.groupby(self.scope1_df['Date'].dt.to_period('M'))['scope1_emissions_kg_co2'].mean()
        scope2_monthly = self.scope2_df.groupby(self.scope2_df['Date'].dt.to_period('M'))['scope2_emissions_kg_co2'].mean()
        scope3_monthly = self.scope3_df.groupby(self.scope3_df['Date'].dt.to_period('M'))['scope3_emissions_kg_co2'].mean()

        # Clean, simple line chart
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=scope1_monthly.index.astype(str), y=scope1_monthly.values,
                                name='Scope 1', line=dict(color='#e74c3c', width=3)))
        fig.add_trace(go.Scatter(x=scope2_monthly.index.astype(str), y=scope2_monthly.values,
                                name='Scope 2', line=dict(color='#3498db', width=3)))
        fig.add_trace(go.Scatter(x=scope3_monthly.index.astype(str), y=scope3_monthly.values,
                                name='Scope 3', line=dict(color='#2ecc71', width=3)))

        fig.update_layout(
            title=None,
            xaxis_title="Month",
            yaxis_title="Emissions (kg CO₂)",
            showlegend=True,
            height=400,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
        st.plotly_chart(fig, use_container_width=True)

        # Simple data center comparison
        st.markdown('<div class="section-header">Data Center Comparison</div>', unsafe_allow_html=True)

        # Calculate averages by data center
        dc_comparison = []
        for dc in ['DC1', 'DC2', 'DC3']:
            scope1_avg = self.scope1_df[self.scope1_df['DataCenterID'] == dc]['scope1_emissions_kg_co2'].mean()
            scope2_avg = self.scope2_df[self.scope2_df['DataCenterID'] == dc]['scope2_emissions_kg_co2'].mean()
            scope3_avg = self.scope3_df[self.scope3_df['DataCenterID'] == dc]['scope3_emissions_kg_co2'].mean()
            total = scope1_avg + scope2_avg + scope3_avg

            dc_comparison.append({
                'Data Center': dc,
                'Total Emissions': f"{total:,.0f} kg CO₂",
                'Scope 1': f"{scope1_avg:,.0f}",
                'Scope 2': f"{scope2_avg:,.0f}",
                'Scope 3': f"{scope3_avg:,.0f}"
            })

        # Display as a clean table
        dc_df = pd.DataFrame(dc_comparison)
        st.dataframe(dc_df, use_container_width=True, hide_index=True)
    
    def show_scenario_analysis(self):
        """Clean scenario analysis interface"""
        st.markdown('<h1 class="main-header">Scenario Analysis</h1>', unsafe_allow_html=True)
        st.markdown("Adjust parameters to see real-time emission predictions")

        # Simple scope selector
        scope_option = st.selectbox(
            "Select Emission Scope",
            ["Scope 1 - Direct Emissions", "Scope 2 - Electricity", "Scope 3 - Supply Chain"],
            help="Choose which emission scope to analyze"
        )

        if "Scope 1" in scope_option:
            self.scope1_scenario_analysis()
        elif "Scope 2" in scope_option:
            self.scope2_scenario_analysis()
        else:
            self.scope3_scenario_analysis()
    
    def scope1_scenario_analysis(self):
        """Clean Scope 1 analysis"""
        st.markdown('<div class="section-header">Direct Emissions Calculator</div>', unsafe_allow_html=True)

        # Simplified input layout
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Energy Sources**")
            diesel_consumption = st.slider("Diesel Fuel (L/day)", 0, 500, 250, help="Daily diesel consumption")
            gas_consumption = st.slider("Natural Gas (kWh/day)", 0, 2000, 1000, help="Daily gas consumption")

        with col2:
            st.markdown("**Other Sources**")
            refrigerant_type = st.selectbox("Refrigerant Type", ["R-134a", "R-410a"])
            refrigerant_leak = st.slider("Refrigerant Leak (kg/year)", 0, 20, 5)
            vehicle_consumption = st.slider("Vehicle Fuel (L/day)", 0, 400, 200)

        # Simple emission calculations
        diesel_emissions = diesel_consumption * 2.68 * 365  # Annual
        gas_emissions = gas_consumption * 0.0053 * 365
        refrigerant_gwp = 2088 if refrigerant_type == "R-410a" else 1430
        refrigerant_emissions = refrigerant_leak * refrigerant_gwp / 1000
        vehicle_emissions = vehicle_consumption * 2.5 * 365  # Average factor

        total_scope1 = diesel_emissions + gas_emissions + refrigerant_emissions + vehicle_emissions

        # Clean results display
        st.markdown(f"""
        <div class="prediction-box">
            <h3> Scope 1 Annual Emissions</h3>
            <h2>{total_scope1:,.0f} kg CO₂</h2>
            <p>vs Average: {total_scope1 - 351000:+,.0f} kg CO₂</p>
        </div>
        """, unsafe_allow_html=True)

        # Breakdown
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Diesel", f"{diesel_emissions:,.0f} kg")
        with col2:
            st.metric("Natural Gas", f"{gas_emissions:,.0f} kg")
        with col3:
            st.metric("Refrigerant", f"{refrigerant_emissions:,.0f} kg")
        with col4:
            st.metric("Vehicles", f"{vehicle_emissions:,.0f} kg")
    
    def scope2_scenario_analysis(self):
        """Clean Scope 2 analysis"""
        st.markdown('<div class="section-header">⚡ Electricity Emissions Calculator</div>', unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Electricity Usage**")
            total_consumption = st.slider("Annual Electricity (MWh)", 500, 2000, 1000, help="Total annual consumption")
            grid_emission_factor = st.slider("Grid Emission Factor", 0.2, 0.8, 0.45, help="kg CO₂ per kWh")

        with col2:
            st.markdown("**Clean Energy**")
            renewable_percentage = st.slider("Renewable Energy (%)", 0, 80, 30, help="% from renewable sources")
            solar_production = st.slider("Solar Production (MWh)", 0, 500, 100, help="On-site solar generation")

        # Simple calculations (convert MWh to kWh)
        total_kwh = total_consumption * 1000
        solar_kwh = solar_production * 1000
        net_consumption = max(0, total_kwh - solar_kwh)
        scope2_emissions = net_consumption * grid_emission_factor * (1 - renewable_percentage/100)

        # Clean results
        st.markdown(f"""
        <div class="prediction-box">
            <h3>⚡ Scope 2 Annual Emissions</h3>
            <h2>{scope2_emissions:,.0f} kg CO₂</h2>
            <p>Net Grid Usage: {net_consumption:,.0f} kWh</p>
        </div>
        """, unsafe_allow_html=True)

        # Impact breakdown
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Grid Factor", f"{grid_emission_factor:.3f} kg/kWh")
        with col2:
            st.metric("Renewable Impact", f"-{renewable_percentage}%")
        with col3:
            st.metric("Solar Offset", f"{solar_kwh:,.0f} kWh")
    
    def scope3_scenario_analysis(self):
        """Clean Scope 3 analysis"""
        st.markdown('<div class="section-header">Supply Chain Emissions Calculator</div>', unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Supply Chain**")
            hardware_volume = st.slider("Hardware Units/Year", 0, 1000, 500, help="Annual hardware procurement")
            supplier_factor = st.slider("Supplier Emission Factor", 0.1, 1.5, 0.6, help="kg CO₂ per $ spent")
            transport_distance = st.slider("Avg Transport (km)", 500, 10000, 3000, help="Average shipping distance")

        with col2:
            st.markdown("**Operations**")
            employee_count = st.slider("Employee Count", 50, 500, 200, help="Total employees")
            avg_commute = st.slider("Avg Commute (km)", 5, 80, 25, help="Daily commute distance")
            business_travel = st.slider("Business Travel (km/employee)", 0, 20000, 5000, help="Annual travel per employee")

        # Simplified calculations
        procurement_emissions = hardware_volume * 800 * supplier_factor  # kg CO₂ per unit
        transport_emissions = transport_distance * hardware_volume * 0.1  # kg CO₂ per km per unit
        commute_emissions = employee_count * avg_commute * 250 * 0.12 * 2  # Round trip, 250 days
        travel_emissions = employee_count * business_travel * 0.15  # kg CO₂ per km

        total_scope3 = procurement_emissions + transport_emissions + commute_emissions + travel_emissions

        # Clean results
        st.markdown(f"""
        <div class="prediction-box">
            <h3> Scope 3 Annual Emissions</h3>
            <h2>{total_scope3:,.0f} kg CO₂</h2>
            <p>Supply Chain + Operations</p>
        </div>
        """, unsafe_allow_html=True)

        # Breakdown
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Procurement", f"{procurement_emissions:,.0f} kg")
        with col2:
            st.metric("Transport", f"{transport_emissions:,.0f} kg")
        with col3:
            st.metric("Commuting", f"{commute_emissions:,.0f} kg")
        with col4:
            st.metric("Travel", f"{travel_emissions:,.0f} kg")

    def show_prescriptive_analytics(self):
        """DICE-based prescriptive analytics interface"""
        st.markdown('<h1 class="main-header">Prescriptive Analytics</h1>', unsafe_allow_html=True)

        if not self.prescriptive_loaded:
            st.warning("Prescriptive analytics not available. Please ensure models are trained and DICE is installed.")
            return

        # Scope selection
        scope_names = {
            "scope1": "Scope 1 - Direct Emissions",
            "scope2": "Scope 2 - Electricity",
            "scope3": "Scope 3 - Supply Chain"
        }
        scope_option = st.selectbox(
            "Select Emission Scope for Analysis",
            ["scope1", "scope2", "scope3"],
            format_func=lambda x: scope_names[x]
        )

        st.markdown(f'<div class="section-header">Target-Based Recommendations for {scope_option.upper()}</div>', unsafe_allow_html=True)

        # Get current average values as baseline
        if scope_option in self.prescriptive.processed_data:
            data = self.prescriptive.processed_data[scope_option]['data']
            feature_names = self.prescriptive.processed_data[scope_option]['features']

            # Current baseline (average values)
            current_values = data[feature_names].mean().values
            current_emission = self.prescriptive.models[scope_option]['model'].predict([current_values])[0]

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Current Baseline**")
                st.metric("Current Emission Level", f"{current_emission:,.0f} kg CO₂")

                # Target setting
                reduction_pct = st.slider("Target Reduction (%)", 5, 50, 20, help="Desired emission reduction percentage")
                target_emission = current_emission * (1 - reduction_pct/100)
                st.metric("Target Emission Level", f"{target_emission:,.0f} kg CO₂",
                         delta=f"-{current_emission - target_emission:,.0f} kg CO₂")

            with col2:
                st.markdown("**Analysis Options**")
                analysis_type = st.radio(
                    "Choose Analysis Type",
                    ["Quick Wins", "Counterfactual Recommendations", "Reduction Potential"]
                )

            # Perform selected analysis
            if st.button("Generate Recommendations", type="primary"):
                with st.spinner("Analyzing with DICE..."):

                    if analysis_type == "Quick Wins":
                        quick_wins = self.prescriptive.get_quick_wins(scope_option, current_values, reduction_pct)

                        if quick_wins:
                            st.markdown("### Quick Win Opportunities")
                            for i, win in enumerate(quick_wins[:3], 1):
                                st.markdown(f"**{i}. {win['feature']}**")
                                col1, col2, col3 = st.columns(3)
                                with col1:
                                    st.metric("Adjustment", f"×{win['adjustment_factor']:.2f}")
                                with col2:
                                    st.metric("New Value", f"{win['new_value']:.2f}")
                                with col3:
                                    st.metric("Reduction", f"{win['reduction_percentage']:.1f}%")
                        else:
                            st.info("No quick wins identified for the current parameters.")

                    elif analysis_type == "Counterfactual Recommendations":
                        recommendations = self.prescriptive.get_feature_recommendations(scope_option, current_values, target_emission)

                        if recommendations:
                            st.markdown("### DICE Counterfactual Recommendations")
                            st.markdown("These changes are most likely to achieve your target emission level:")

                            for i, rec in enumerate(recommendations[:5], 1):
                                with st.expander(f"{i}. {rec['feature']} (appears in {rec['frequency']} scenarios)"):
                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.metric("Current", f"{rec['current_value']:.2f}")
                                    with col2:
                                        st.metric("Recommended", f"{rec['new_value']:.2f}")
                                    with col3:
                                        st.metric("Change", f"{rec['recommended_change_pct']:+.1f}%")
                        else:
                            st.warning("Could not generate counterfactual recommendations. Try adjusting the target.")

                    else:  # Reduction Potential
                        potential = self.prescriptive.calculate_reduction_potential(scope_option, current_values)

                        if potential:
                            st.markdown("### Maximum Reduction Potential")

                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("Current Emissions", f"{potential['current_emission']:,.0f} kg CO₂")
                            with col2:
                                st.metric("Optimized Emissions", f"{potential['optimized_emission']:,.0f} kg CO₂")
                            with col3:
                                st.metric("Max Reduction", f"{potential['reduction_percentage']:.1f}%")

                            st.markdown("**Key Optimization Areas:**")
                            # Show top changes in optimized scenario
                            for i, (feature, current_val, opt_val) in enumerate(zip(feature_names, current_values, potential['optimized_values'])):
                                if abs(opt_val - current_val) / current_val > 0.1:  # >10% change
                                    change_pct = ((opt_val - current_val) / current_val) * 100
                                    st.markdown(f"- **{feature}**: {change_pct:+.1f}% change")
                        else:
                            st.error("Could not calculate reduction potential.")

     

def main():
    """Clean, simple main application"""
    app = ESGStreamlitApp()

    # Simple top navigation
    _, col2, _ = st.columns([1, 2, 1])
    with col2:
        page = st.selectbox(
            "Navigate",
            ["Dashboard", "Scenario Analysis", "Prescriptive Analytics"],
            label_visibility="collapsed"
        )

    # Page routing
    if page == "Dashboard":
        app.show_dashboard()
    elif page == "Scenario Analysis":
        app.show_scenario_analysis()
    else:
        app.show_prescriptive_analytics()

  

if __name__ == "__main__":
    main()
