import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
import os
from .data_preprocessing import DataPreprocessor, load_and_prepare_training_data

class ESGModelTrainer:
    def __init__(self):
        self.models = {}
        self.preprocessor = DataPreprocessor()
        self.model_params = {
            'scope1': {
                'n_estimators': 200,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42
            },
            'scope2': {
                'n_estimators': 150,
                'max_depth': 5,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42
            },
            'scope3': {
                'n_estimators': 250,
                'max_depth': 7,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42
            }
        }
    
    def train_scope_model(self, df, scope, target_column):
        """Train XGBoost model for a specific scope"""
        print(f"Training {scope} model...")
        
        # Prepare data
        df_processed = self.preprocessor.prepare_scope_data(df, scope, target_column, fit=True)
        
        # Split data
        X_train, X_test, y_train, y_test = self.preprocessor.split_data(df_processed, target_column)
        
        # Train model
        model = xgb.XGBRegressor(**self.model_params[scope])
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred_train = model.predict(X_train)
        y_pred_test = model.predict(X_test)
        
        # Calculate metrics
        train_metrics = {
            'mse': mean_squared_error(y_train, y_pred_train),
            'rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
            'mae': mean_absolute_error(y_train, y_pred_train),
            'r2': r2_score(y_train, y_pred_train)
        }
        
        test_metrics = {
            'mse': mean_squared_error(y_test, y_pred_test),
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'mae': mean_absolute_error(y_test, y_pred_test),
            'r2': r2_score(y_test, y_pred_test)
        }
        
        print(f"{scope} Model Performance:")
        print(f"Train R²: {train_metrics['r2']:.4f}, Test R²: {test_metrics['r2']:.4f}")
        print(f"Train RMSE: {train_metrics['rmse']:.2f}, Test RMSE: {test_metrics['rmse']:.2f}")
        
        # Store model
        self.models[scope] = model
        
        return model, train_metrics, test_metrics
    
    def train_all_models(self):
        """Train models for all scopes"""
        # Load training data
        df1, df2, df3 = load_and_prepare_training_data()
        
        # Train models
        scope1_model, scope1_train_metrics, scope1_test_metrics = self.train_scope_model(
            df1, 'scope1', 'scope1_emissions'
        )
        
        scope2_model, scope2_train_metrics, scope2_test_metrics = self.train_scope_model(
            df2, 'scope2', 'scope2_emissions'
        )
        
        scope3_model, scope3_train_metrics, scope3_test_metrics = self.train_scope_model(
            df3, 'scope3', 'scope3_emissions'
        )
        
        return {
            'scope1': {'model': scope1_model, 'train_metrics': scope1_train_metrics, 'test_metrics': scope1_test_metrics},
            'scope2': {'model': scope2_model, 'train_metrics': scope2_train_metrics, 'test_metrics': scope2_test_metrics},
            'scope3': {'model': scope3_model, 'train_metrics': scope3_train_metrics, 'test_metrics': scope3_test_metrics}
        }
    
    def save_models(self, model_dir='models'):
        """Save trained models and preprocessor"""
        os.makedirs(model_dir, exist_ok=True)
        
        for scope, model in self.models.items():
            model_path = os.path.join(model_dir, f'{scope}_model.joblib')
            joblib.dump(model, model_path)
            print(f"Saved {scope} model to {model_path}")
        
        # Save preprocessor
        preprocessor_path = os.path.join(model_dir, 'preprocessor.joblib')
        joblib.dump(self.preprocessor, preprocessor_path)
        print(f"Saved preprocessor to {preprocessor_path}")
    
    def load_models(self, model_dir='models'):
        """Load trained models and preprocessor"""
        scopes = ['scope1', 'scope2', 'scope3']
        
        for scope in scopes:
            model_path = os.path.join(model_dir, f'{scope}_model.joblib')
            if os.path.exists(model_path):
                self.models[scope] = joblib.load(model_path)
                print(f"Loaded {scope} model from {model_path}")
        
        # Load preprocessor
        preprocessor_path = os.path.join(model_dir, 'preprocessor.joblib')
        if os.path.exists(preprocessor_path):
            self.preprocessor = joblib.load(preprocessor_path)
            print(f"Loaded preprocessor from {preprocessor_path}")
    
    def predict_emissions(self, df, scope):
        """Make predictions for a specific scope"""
        if scope not in self.models:
            raise ValueError(f"Model for {scope} not found. Please train the model first.")
        
        # Prepare data
        df_processed = self.preprocessor.prepare_scope_data(df, scope, fit=False)
        
        # Remove non-feature columns
        feature_cols = [col for col in df_processed.columns 
                       if col not in ['datacenter_id']]
        X = df_processed[feature_cols]
        
        # Make predictions
        predictions = self.models[scope].predict(X)
        
        return predictions
    
    def get_feature_importance(self, scope):
        """Get feature importance for a specific scope"""
        if scope not in self.models:
            raise ValueError(f"Model for {scope} not found.")
        
        model = self.models[scope]
        feature_names = self.preprocessor.get_feature_names(scope)
        
        # Get feature importance
        importance = model.feature_importances_
        
        # Create DataFrame
        importance_df = pd.DataFrame({
            'feature': feature_names[:len(importance)],
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        return importance_df

def train_and_save_models():
    """Main function to train and save all models"""
    trainer = ESGModelTrainer()
    
    # Train all models
    results = trainer.train_all_models()
    
    # Save models
    trainer.save_models()
    
    return trainer, results

if __name__ == "__main__":
    trainer, results = train_and_save_models()
    print("Model training completed!")
