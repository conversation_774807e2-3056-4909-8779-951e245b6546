import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class HistoricalDataManager:
    def __init__(self, data_dir='data'):
        self.data_dir = data_dir
        self.historical_file = os.path.join(data_dir, 'historical_emissions.csv')
        self.ensure_historical_data_exists()
    
    def ensure_historical_data_exists(self):
        """Create historical emissions data if it doesn't exist"""
        if not os.path.exists(self.historical_file):
            self.create_historical_data()
    
    def create_historical_data(self):
        """Create sample historical emissions data from existing datasets"""
        print("Creating historical emissions data...")
        
        # Load existing datasets
        try:
            df1 = pd.read_csv('scope1_synthetic_2023_2024_v2.csv')
            df2 = pd.read_csv('scope2_synthetic_2023_2024_daily.csv')
            df3 = pd.read_csv('scope3_synthetic_2023_2024.csv')
        except FileNotFoundError as e:
            print(f"Error loading data: {e}")
            return
        
        # Calculate actual emissions for historical data
        historical_data = []
        
        # Get unique datacenter-date combinations
        dates = pd.to_datetime(df1['date']).unique()
        datacenters = df1['datacenter_id'].unique()
        
        for date in dates:
            for dc in datacenters:
                # Get data for this datacenter and date
                df1_row = df1[(df1['datacenter_id'] == dc) & (df1['date'] == str(date.date()))]
                df2_row = df2[(df2['datacenter_id'] == dc) & (df2['date'] == str(date.date()))]
                df3_row = df3[(df3['datacenter_id'] == dc) & (df3['date'] == str(date.date()))]
                
                if not df1_row.empty and not df2_row.empty and not df3_row.empty:
                    # Calculate Scope 1 emissions
                    scope1_emissions = (
                        df1_row['diesel_fuel_consumption_l_per_day'].iloc[0] * 2.68 +
                        df1_row['natural_gas_consumption_m3_per_day'].iloc[0] * 1.93 +
                        df1_row['refrigerant_leak_volume_kg_per_year'].iloc[0] / 365 * 2088 +
                        df1_row['vehicle_fuel_consumption_l_per_day'].iloc[0] * 2.68
                    )
                    
                    # Calculate Scope 2 emissions
                    scope2_emissions = (
                        (df2_row['total_energy_consumption_kwh_per_day'].iloc[0] - 
                         df2_row['on_site_solar_generation_kWh_per_day'].iloc[0]) * 
                        df2_row['grid_emission_factor_kgco2perkwh'].iloc[0]
                    )
                    
                    # Calculate Scope 3 emissions
                    scope3_emissions = (
                        df3_row['business_travel_emissions_kgco2'].iloc[0] +
                        df3_row['commuting_emissions_kgco2'].iloc[0] +
                        df3_row['waste_treatment_emissions_kgco2'].iloc[0] +
                        df3_row['use_phase_emissions_kgco2'].iloc[0] +
                        df3_row['end_of_life_emissions_kgco2'].iloc[0] +
                        df3_row['purchased_goods_emissions_kgco2'].iloc[0]
                    )
                    
                    historical_data.append({
                        'date': date,
                        'datacenter_id': dc,
                        'location_country': df1_row['location_country'].iloc[0],
                        'scope1_emissions': scope1_emissions,
                        'scope2_emissions': scope2_emissions,
                        'scope3_emissions': scope3_emissions,
                        'total_emissions': scope1_emissions + scope2_emissions + scope3_emissions
                    })
        
        # Create DataFrame and save
        historical_df = pd.DataFrame(historical_data)
        historical_df.to_csv(self.historical_file, index=False)
        print(f"Historical data saved to {self.historical_file}")
    
    def get_historical_data(self, start_date=None, end_date=None, datacenter_ids=None):
        """Retrieve historical emissions data"""
        df = pd.read_csv(self.historical_file)
        df['date'] = pd.to_datetime(df['date'])
        
        # Filter by date range
        if start_date:
            df = df[df['date'] >= pd.to_datetime(start_date)]
        if end_date:
            df = df[df['date'] <= pd.to_datetime(end_date)]
        
        # Filter by datacenter IDs
        if datacenter_ids:
            df = df[df['datacenter_id'].isin(datacenter_ids)]
        
        return df
    
    def get_company_totals(self, start_date=None, end_date=None):
        """Get company-wide emission totals"""
        df = self.get_historical_data(start_date, end_date)
        
        # Group by date and sum across all datacenters
        company_totals = df.groupby('date').agg({
            'scope1_emissions': 'sum',
            'scope2_emissions': 'sum',
            'scope3_emissions': 'sum',
            'total_emissions': 'sum'
        }).reset_index()
        
        return company_totals
    
    def get_datacenter_totals(self, start_date=None, end_date=None):
        """Get datacenter-wise emission totals"""
        df = self.get_historical_data(start_date, end_date)
        
        # Group by datacenter and sum across time period
        datacenter_totals = df.groupby(['datacenter_id', 'location_country']).agg({
            'scope1_emissions': 'sum',
            'scope2_emissions': 'sum',
            'scope3_emissions': 'sum',
            'total_emissions': 'sum'
        }).reset_index()
        
        return datacenter_totals
    
    def get_scope_wise_trends(self, start_date=None, end_date=None, aggregation='daily'):
        """Get scope-wise emission trends"""
        df = self.get_historical_data(start_date, end_date)
        
        if aggregation == 'weekly':
            df['period'] = df['date'].dt.to_period('W')
        elif aggregation == 'monthly':
            df['period'] = df['date'].dt.to_period('M')
        elif aggregation == 'quarterly':
            df['period'] = df['date'].dt.to_period('Q')
        elif aggregation == 'yearly':
            df['period'] = df['date'].dt.to_period('Y')
        else:
            df['period'] = df['date']
        
        # Group by period and sum
        trends = df.groupby('period').agg({
            'scope1_emissions': 'sum',
            'scope2_emissions': 'sum',
            'scope3_emissions': 'sum',
            'total_emissions': 'sum'
        }).reset_index()
        
        return trends
    
    def add_historical_record(self, date, datacenter_id, location_country, 
                            scope1_emissions, scope2_emissions, scope3_emissions):
        """Add a new historical record"""
        new_record = {
            'date': pd.to_datetime(date),
            'datacenter_id': datacenter_id,
            'location_country': location_country,
            'scope1_emissions': scope1_emissions,
            'scope2_emissions': scope2_emissions,
            'scope3_emissions': scope3_emissions,
            'total_emissions': scope1_emissions + scope2_emissions + scope3_emissions
        }
        
        # Load existing data
        if os.path.exists(self.historical_file):
            df = pd.read_csv(self.historical_file)
            df['date'] = pd.to_datetime(df['date'])
        else:
            df = pd.DataFrame()
        
        # Add new record
        new_df = pd.DataFrame([new_record])
        df = pd.concat([df, new_df], ignore_index=True)
        
        # Remove duplicates (same date and datacenter)
        df = df.drop_duplicates(subset=['date', 'datacenter_id'], keep='last')
        
        # Sort by date
        df = df.sort_values('date')
        
        # Save
        df.to_csv(self.historical_file, index=False)
    
    def get_latest_date(self):
        """Get the latest date in historical data"""
        if os.path.exists(self.historical_file):
            df = pd.read_csv(self.historical_file)
            df['date'] = pd.to_datetime(df['date'])
            return df['date'].max()
        return None
    
    def get_datacenter_list(self):
        """Get list of all datacenters in historical data"""
        if os.path.exists(self.historical_file):
            df = pd.read_csv(self.historical_file)
            return df[['datacenter_id', 'location_country']].drop_duplicates().to_dict('records')
        return []
    
    def get_emission_statistics(self, start_date=None, end_date=None):
        """Get emission statistics for the specified period"""
        df = self.get_historical_data(start_date, end_date)
        
        if df.empty:
            return {}
        
        stats = {
            'total_scope1': df['scope1_emissions'].sum(),
            'total_scope2': df['scope2_emissions'].sum(),
            'total_scope3': df['scope3_emissions'].sum(),
            'total_emissions': df['total_emissions'].sum(),
            'avg_daily_scope1': df.groupby('date')['scope1_emissions'].sum().mean(),
            'avg_daily_scope2': df.groupby('date')['scope2_emissions'].sum().mean(),
            'avg_daily_scope3': df.groupby('date')['scope3_emissions'].sum().mean(),
            'avg_daily_total': df.groupby('date')['total_emissions'].sum().mean(),
            'num_datacenters': df['datacenter_id'].nunique(),
            'date_range': {
                'start': df['date'].min(),
                'end': df['date'].max(),
                'days': (df['date'].max() - df['date'].min()).days + 1
            }
        }
        
        return stats

# Create a global instance
historical_manager = HistoricalDataManager()
