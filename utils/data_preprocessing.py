import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class DataPreprocessor:
    def __init__(self):
        self.label_encoders = {}
        self.scalers = {}
        self.feature_columns = {
            'scope1': [
                'facility_age_years', 'ambient_temperature_C', 'IT_load_kW',
                'diesel_generator_hours_per_day', 'generator_capacity_kW', 'generator_efficiency_pct',
                'hvac_runtime_hours_per_day', 'occupancy_pct', 'heating_degree_days',
                'boiler_efficiency_pct', 'natural_gas_consumption_m3_per_day',
                'refrigerant_leak_volume_kg_per_year', 'test_cycles', 'incident_count',
                'fire_suppression_discharges_per_year', 'fleet_vehicle_count',
                'avg_km_per_vehicle_per_day', 'fuel_efficiency_kmpl', 'vehicle_fuel_consumption_l_per_day'
            ],
            'scope2': [
                'ambient_temperature_C', 'it_load_kw', 'cooling_system_power_draw_kW',
                'UPS_loss_factor', 'power_usage_effectiveness', 'solar_capacity_kW',
                'sunlight_hours_per_day', 'solar_panel_efficiency_pct',
                'on_site_solar_generation_kWh_per_day', 'total_energy_consumption_kwh_per_day'
            ],
            'scope3': [
                'employee_count', 'travel_km_per_trip', 'waste_volume_kg',
                'product_units_sold', 'product_use_duration_yrs', 'purchase_volume_kg'
            ]
        }
        self.categorical_columns = {
            'scope1': ['location_country', 'climate_zone', 'chiller_type', 'refrigerant_type',
                      'fire_suppression_type', 'vehicle_fuel_type'],
            'scope2': ['location_country', 'climate_zone'],
            'scope3': ['location_country', 'climate_zone', 'travel_mode', 'waste_type']
        }
    
    def prepare_date_features(self, df):
        """Extract date features from date column"""
        df = df.copy()
        df['date'] = pd.to_datetime(df['date'])
        df['year'] = df['date'].dt.year
        df['month'] = df['date'].dt.month
        df['day_of_year'] = df['date'].dt.dayofyear
        df['quarter'] = df['date'].dt.quarter
        df['is_weekend'] = df['date'].dt.weekday >= 5
        return df
    
    def encode_categorical_features(self, df, scope, fit=True):
        """Encode categorical features for the specified scope"""
        df = df.copy()
        categorical_cols = self.categorical_columns.get(scope, [])
        
        for col in categorical_cols:
            if col in df.columns:
                if fit:
                    if col not in self.label_encoders:
                        self.label_encoders[col] = LabelEncoder()
                    df[col] = self.label_encoders[col].fit_transform(df[col].astype(str))
                else:
                    if col in self.label_encoders:
                        # Handle unseen categories
                        unique_values = set(df[col].astype(str))
                        known_values = set(self.label_encoders[col].classes_)
                        unknown_values = unique_values - known_values
                        
                        if unknown_values:
                            # Map unknown values to the most frequent class
                            most_frequent = self.label_encoders[col].classes_[0]
                            df[col] = df[col].astype(str).replace(list(unknown_values), most_frequent)
                        
                        df[col] = self.label_encoders[col].transform(df[col].astype(str))
        return df
    
    def scale_features(self, df, scope, fit=True):
        """Scale numerical features for the specified scope"""
        df = df.copy()
        feature_cols = self.feature_columns.get(scope, [])
        
        # Add date features to scaling
        date_features = ['year', 'month', 'day_of_year', 'quarter']
        feature_cols.extend(date_features)
        
        # Only scale columns that exist in the dataframe
        cols_to_scale = [col for col in feature_cols if col in df.columns]
        
        if cols_to_scale:
            if fit:
                if scope not in self.scalers:
                    self.scalers[scope] = StandardScaler()
                df[cols_to_scale] = self.scalers[scope].fit_transform(df[cols_to_scale])
            else:
                if scope in self.scalers:
                    df[cols_to_scale] = self.scalers[scope].transform(df[cols_to_scale])
        
        return df
    
    def prepare_scope_data(self, df, scope, target_column=None, fit=True):
        """Prepare data for a specific scope"""
        df = df.copy()
        
        # Add date features
        df = self.prepare_date_features(df)
        
        # Encode categorical features
        df = self.encode_categorical_features(df, scope, fit=fit)
        
        # Get feature columns for this scope
        feature_cols = self.feature_columns.get(scope, [])
        categorical_cols = self.categorical_columns.get(scope, [])
        date_features = ['year', 'month', 'day_of_year', 'quarter', 'is_weekend']
        
        # Combine all feature columns
        all_feature_cols = feature_cols + categorical_cols + date_features
        
        # Only keep columns that exist in the dataframe
        available_cols = [col for col in all_feature_cols if col in df.columns]
        
        # Add datacenter_id for grouping
        if 'datacenter_id' in df.columns:
            available_cols.append('datacenter_id')
        
        # Add target column if specified
        if target_column and target_column in df.columns:
            available_cols.append(target_column)
        
        # Select only available columns
        df_scope = df[available_cols].copy()
        
        # Scale features
        df_scope = self.scale_features(df_scope, scope, fit=fit)
        
        return df_scope
    
    def split_data(self, df, target_column, test_size=0.2, random_state=42):
        """Split data into train and test sets"""
        X = df.drop(columns=[target_column, 'datacenter_id'] if 'datacenter_id' in df.columns else [target_column])
        y = df[target_column]
        
        return train_test_split(X, y, test_size=test_size, random_state=random_state)
    
    def get_feature_names(self, scope):
        """Get feature names for a specific scope"""
        feature_cols = self.feature_columns.get(scope, [])
        categorical_cols = self.categorical_columns.get(scope, [])
        date_features = ['year', 'month', 'day_of_year', 'quarter', 'is_weekend']
        
        return feature_cols + categorical_cols + date_features

def load_and_prepare_training_data():
    """Load and prepare training data for all scopes"""
    # Load the datasets
    df1 = pd.read_csv('scope1_synthetic_2023_2024_v2.csv')
    df2 = pd.read_csv('scope2_synthetic_2023_2024_daily.csv')
    df3 = pd.read_csv('scope3_synthetic_2023_2024.csv')
    
    # Calculate target variables (emissions) using emission factors
    emission_factors = pd.read_csv('data/emission_factors.csv')
    
    # For scope 1: Calculate emissions from fuel consumption and other sources
    df1['scope1_emissions'] = (
        df1['diesel_fuel_consumption_l_per_day'] * 2.68 +  # Diesel emissions
        df1['natural_gas_consumption_m3_per_day'] * 1.93 +  # Natural gas emissions
        df1['refrigerant_leak_volume_kg_per_year'] / 365 * 2088 +  # Refrigerant leaks (daily)
        df1['vehicle_fuel_consumption_l_per_day'] * 2.68  # Vehicle fuel emissions
    )
    
    # For scope 2: Calculate emissions from electricity consumption
    df2['scope2_emissions'] = (
        (df2['total_energy_consumption_kwh_per_day'] - df2['on_site_solar_generation_kWh_per_day']) * 
        df2['grid_emission_factor_kgco2perkwh']
    )
    
    # For scope 3: Use the existing emission columns
    df3['scope3_emissions'] = (
        df3['business_travel_emissions_kgco2'] +
        df3['commuting_emissions_kgco2'] +
        df3['waste_treatment_emissions_kgco2'] +
        df3['use_phase_emissions_kgco2'] +
        df3['end_of_life_emissions_kgco2'] +
        df3['purchased_goods_emissions_kgco2']
    )
    
    return df1, df2, df3
