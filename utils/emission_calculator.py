import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from .model_training import ESGModelTrainer

class EmissionCalculator:
    def __init__(self):
        self.emission_factors = pd.read_csv('data/emission_factors.csv')
        self.model_trainer = ESGModelTrainer()
        self.model_trainer.load_models()
    
    def get_emission_factor(self, country, emission_source):
        """Get emission factor for a specific country and source"""
        factor_row = self.emission_factors[
            (self.emission_factors['country'] == country) & 
            (self.emission_factors['emission_source'] == emission_source)
        ]
        
        if not factor_row.empty:
            return factor_row['emission_factor'].iloc[0]
        else:
            # Default to Australia if country not found
            factor_row = self.emission_factors[
                (self.emission_factors['country'] == 'Australia') & 
                (self.emission_factors['emission_source'] == emission_source)
            ]
            return factor_row['emission_factor'].iloc[0] if not factor_row.empty else 1.0
    
    def calculate_scope1_emissions(self, df):
        """Calculate Scope 1 emissions using driver-based approach"""
        emissions = []
        
        for _, row in df.iterrows():
            country = row['location_country']
            
            # Diesel fuel emissions
            diesel_ef = self.get_emission_factor(country, 'diesel_fuel')
            diesel_emissions = row.get('diesel_fuel_consumption_l_per_day', 0) * diesel_ef
            
            # Natural gas emissions
            gas_ef = self.get_emission_factor(country, 'natural_gas')
            gas_emissions = row.get('natural_gas_consumption_m3_per_day', 0) * gas_ef
            
            # Refrigerant emissions
            refrigerant_type = row.get('refrigerant_type', 'R-410A').lower().replace('-', '')
            ref_ef = self.get_emission_factor(country, f'refrigerant_{refrigerant_type}')
            ref_emissions = row.get('refrigerant_leak_volume_kg_per_year', 0) / 365 * ref_ef
            
            # Fire suppression emissions
            fire_type = row.get('fire_suppression_type', 'Novec').lower()
            fire_ef = self.get_emission_factor(country, f'fire_suppression_{fire_type}')
            fire_emissions = row.get('fire_suppression_discharges_per_year', 0) / 365 * fire_ef
            
            # Vehicle fuel emissions
            vehicle_emissions = row.get('vehicle_fuel_consumption_l_per_day', 0) * diesel_ef
            
            total_scope1 = diesel_emissions + gas_emissions + ref_emissions + fire_emissions + vehicle_emissions
            emissions.append(total_scope1)
        
        return np.array(emissions)
    
    def calculate_scope2_emissions(self, df):
        """Calculate Scope 2 emissions using driver-based approach"""
        emissions = []
        
        for _, row in df.iterrows():
            country = row['location_country']
            
            # Grid electricity emissions
            grid_ef = self.get_emission_factor(country, 'electricity_grid')
            
            # Net electricity consumption (total - solar generation)
            total_consumption = row.get('total_energy_consumption_kwh_per_day', 0)
            solar_generation = row.get('on_site_solar_generation_kWh_per_day', 0)
            net_consumption = max(0, total_consumption - solar_generation)
            
            scope2_emissions = net_consumption * grid_ef
            emissions.append(scope2_emissions)
        
        return np.array(emissions)
    
    def calculate_scope3_emissions(self, df):
        """Calculate Scope 3 emissions using driver-based approach"""
        emissions = []
        
        for _, row in df.iterrows():
            country = row['location_country']
            
            # Business travel emissions
            travel_mode = row.get('travel_mode', 'car')
            travel_ef = self.get_emission_factor(country, f'business_travel_{travel_mode}')
            travel_km = row.get('travel_km_per_trip', 0) * row.get('employee_count', 0)
            travel_emissions = travel_km * travel_ef
            
            # Commuting emissions (assuming car for simplicity)
            commute_ef = self.get_emission_factor(country, 'commuting_car')
            commute_emissions = travel_km * 0.5 * commute_ef  # Assume 50% of business travel distance
            
            # Waste treatment emissions
            waste_type = row.get('waste_type', 'mixed')
            waste_ef = self.get_emission_factor(country, f'waste_{waste_type}')
            waste_emissions = row.get('waste_volume_kg', 0) * waste_ef
            
            # Purchased goods emissions
            goods_ef = self.get_emission_factor(country, 'purchased_goods')
            goods_emissions = row.get('purchase_volume_kg', 0) * goods_ef
            
            # Product use phase emissions (simplified calculation)
            use_emissions = row.get('product_units_sold', 0) * row.get('product_use_duration_yrs', 0) * 10
            
            # End of life emissions (simplified)
            eol_emissions = row.get('product_units_sold', 0) * 0.5
            
            total_scope3 = (travel_emissions + commute_emissions + waste_emissions + 
                          goods_emissions + use_emissions + eol_emissions)
            emissions.append(total_scope3)
        
        return np.array(emissions)
    
    def predict_and_calculate_emissions(self, df):
        """Predict drivers and calculate emissions for all scopes"""
        results = {}
        
        # Make predictions for each scope
        try:
            scope1_predictions = self.model_trainer.predict_emissions(df, 'scope1')
            results['scope1_ml_predictions'] = scope1_predictions
        except Exception as e:
            print(f"Error predicting Scope 1: {e}")
            results['scope1_ml_predictions'] = np.zeros(len(df))
        
        try:
            scope2_predictions = self.model_trainer.predict_emissions(df, 'scope2')
            results['scope2_ml_predictions'] = scope2_predictions
        except Exception as e:
            print(f"Error predicting Scope 2: {e}")
            results['scope2_ml_predictions'] = np.zeros(len(df))
        
        try:
            scope3_predictions = self.model_trainer.predict_emissions(df, 'scope3')
            results['scope3_ml_predictions'] = scope3_predictions
        except Exception as e:
            print(f"Error predicting Scope 3: {e}")
            results['scope3_ml_predictions'] = np.zeros(len(df))
        
        # Calculate driver-based emissions
        results['scope1_driver_based'] = self.calculate_scope1_emissions(df)
        results['scope2_driver_based'] = self.calculate_scope2_emissions(df)
        results['scope3_driver_based'] = self.calculate_scope3_emissions(df)
        
        # Combine ML predictions with driver-based calculations (weighted average)
        results['scope1_final'] = (0.7 * results['scope1_ml_predictions'] + 
                                 0.3 * results['scope1_driver_based'])
        results['scope2_final'] = (0.7 * results['scope2_ml_predictions'] + 
                                 0.3 * results['scope2_driver_based'])
        results['scope3_final'] = (0.7 * results['scope3_ml_predictions'] + 
                                 0.3 * results['scope3_driver_based'])
        
        return results
    
    def generate_time_series_predictions(self, base_df, start_date, end_date):
        """Generate predictions for a time range"""
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        # Generate date range
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        predictions = []
        
        for date in date_range:
            # Create a copy of base data for this date
            df_date = base_df.copy()
            df_date['date'] = date
            
            # Add some seasonal variation
            day_of_year = date.dayofyear
            seasonal_factor = 1 + 0.1 * np.sin(2 * np.pi * day_of_year / 365)
            
            # Apply seasonal variation to relevant columns
            if 'ambient_temperature_C' in df_date.columns:
                df_date['ambient_temperature_C'] *= seasonal_factor
            if 'IT_load_kW' in df_date.columns:
                df_date['IT_load_kW'] *= (1 + 0.05 * np.random.normal(0, 1, len(df_date)))
            
            # Calculate emissions
            emissions = self.predict_and_calculate_emissions(df_date)
            
            # Store results
            for idx, row in df_date.iterrows():
                predictions.append({
                    'date': date,
                    'datacenter_id': row['datacenter_id'],
                    'scope1_emissions': emissions['scope1_final'][idx],
                    'scope2_emissions': emissions['scope2_final'][idx],
                    'scope3_emissions': emissions['scope3_final'][idx],
                    'total_emissions': (emissions['scope1_final'][idx] + 
                                      emissions['scope2_final'][idx] + 
                                      emissions['scope3_final'][idx])
                })
        
        return pd.DataFrame(predictions)
    
    def aggregate_predictions(self, predictions_df, aggregation_level='daily'):
        """Aggregate predictions by time period"""
        if aggregation_level == 'daily':
            return predictions_df
        elif aggregation_level == 'weekly':
            predictions_df['week'] = predictions_df['date'].dt.isocalendar().week
            return predictions_df.groupby(['week', 'datacenter_id']).agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()
        elif aggregation_level == 'monthly':
            predictions_df['month'] = predictions_df['date'].dt.to_period('M')
            return predictions_df.groupby(['month', 'datacenter_id']).agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()
        elif aggregation_level == 'quarterly':
            predictions_df['quarter'] = predictions_df['date'].dt.to_period('Q')
            return predictions_df.groupby(['quarter', 'datacenter_id']).agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()
        elif aggregation_level == 'yearly':
            predictions_df['year'] = predictions_df['date'].dt.year
            return predictions_df.groupby(['year', 'datacenter_id']).agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()
        else:
            return predictions_df
