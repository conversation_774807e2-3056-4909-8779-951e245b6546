# ESG Emission Prediction Dashboard

A comprehensive Streamlit application for predicting and analyzing ESG emissions from data center operations using XGBoost machine learning models.

## Features

###  Main Dashboard
- **Time Period Selection**: Choose from next month, quarter, year, or custom date range
- **Company-wide Predictions**: View total emissions across all scopes
- **Datacenter Breakdown**: Detailed emission analysis by facility
- **Historical Trends**: Interactive line graphs showing emission patterns
- **SHAP Analysis**: Model interpretability and feature importance

###  Scenario Analysis
- **Parameter Modification**: Adjust operational parameters to see emission impacts
- **Point Predictions**: Real-time emission calculations based on changes
- **Comparison Views**: Side-by-side baseline vs scenario analysis

### Counterfactual Analysis
- **Reduction Targets**: Set percentage reduction goals for each scope
- **Action Recommendations**: AI-powered suggestions for achieving targets
- **Impact Visualization**: See potential emission reductions

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd esg-emission-prediction
```

2. Create and activate virtual environment:
```bash
python -m venv esg_venv
source esg_venv/bin/activate  # On Windows: esg_venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up models and data:
```bash
python setup_models.py
```

5. Run the application:
```bash
streamlit run app.py
```

## Data Requirements

The application expects CSV files with the following structure:

### Required Columns
- `datacenter_id`: Unique identifier for each data center
- `location_country`: Country where the data center is located
- `climate_zone`: Climate classification (Temperate, Cold, Tropical)
- `date`: Date in YYYY-MM-DD format

### Scope 1 Columns
- Diesel fuel consumption, natural gas usage, refrigerant leaks, etc.

### Scope 2 Columns
- IT load, cooling power, solar generation, grid emission factors, etc.

### Scope 3 Columns
- Employee travel, waste generation, purchased goods, etc.

## Model Architecture

- **Algorithm**: XGBoost Regression
- **Approach**: Driver-based emission calculations with ML predictions
- **Features**: Time series, categorical encoding, feature scaling
- **Validation**: Train/test split with cross-validation

## Usage

1. **Upload Data**: Use the sidebar to upload your CSV file
2. **Generate Predictions**: Select time period and click "Generate Predictions"
3. **Analyze Results**: View company totals, datacenter breakdowns, and trends
4. **Scenario Testing**: Modify parameters to test different scenarios
5. **Set Targets**: Use counterfactual analysis for reduction planning

## File Structure

```
├── app.py                 # Main Streamlit application
├── setup_models.py        # Model training and setup script
├── requirements.txt       # Python dependencies
├── data/
│   ├── emission_factors.csv
│   ├── sample_upload_template.csv
│   └── historical_emissions.csv
├── models/
│   ├── scope1_model.joblib
│   ├── scope2_model.joblib
│   ├── scope3_model.joblib
│   └── preprocessor.joblib
└── utils/
    ├── data_preprocessing.py
    ├── model_training.py
    ├── emission_calculator.py
    └── historical_data.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
